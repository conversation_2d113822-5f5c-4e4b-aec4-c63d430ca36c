package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.task_log.service.TaskLog;
import cloud.demand.lab.common.task_log.service.TaskLogService;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.StaticGinstypeDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.yunxiao.GridItemDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.yunxiao.YunxiaoGridReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.yunxiao.YunxiaoGridRes;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsYuxiaoRubikGridDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.YunxiaoGridReserveModeEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.YuxniaoGridStatusEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.SyncYunxiaoRubikGridService;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.YunXiaoUtil;
import cloud.demand.lab.modules.operation_view.util.tencent_cloud.yunxiao.RegionDTO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SyncYunxiaoRubikGridServiceImpl implements SyncYunxiaoRubikGridService {
    private static final int RETRY_TIMES = 3;
    @Resource
    private DBHelper ckcldDBHelper;
    @Resource
    private TaskLogService taskLogService;

    private ExecutorService threadPool = Executors.newWorkStealingPool();

    /**
     * 查询云霄的地域列表
     *
     * @return
     */
    private List<String> getRegionList() {
        List<RegionDTO> regionDTOS = YunXiaoUtil.queryAllRegion().getData();
        Set<String> regions = regionDTOS.stream().map(region -> region.getRegion()).collect(Collectors.toSet());
        log.info("regions: " + regions);
        return regions.stream().collect(Collectors.toList());
    }

    /**
     * 根据地域，查询云霄的预扣块信息
     *
     * @param region
     * @return
     */
    private List<DwsYuxiaoRubikGridDfDO> getGridItems(String region, String date, Map<String, StaticGinstypeDO> instanceModelMap, Map<Long, StaticZoneDO> allPlanZoneInfos) {
        // 一次请求 500 条数据，分批请求
        int offset = 0;
        int limit = 1000000;

        Function<int[], YunxiaoGridRes.YunxiaoGridDataRes> request = (int[] page) -> {
            // 请求云霄接口，重试 3 次，如果 1 次失败 30s 后再次尝试
            int tryTimesLeft = RETRY_TIMES;

            while (tryTimesLeft > 0) {
                try {
                    String result = YunXiaoUtil.postRaw("/rubik/grid/export-to-json", new YunxiaoGridReq(region, page[0], page[1]), RETRY_TIMES, 10000);
                    YunxiaoGridRes.YunxiaoGridDataRes resp = JSON.parse(result, YunxiaoGridRes.YunxiaoGridDataRes.class);
                    return resp;
                } catch (Exception e) {
                    tryTimesLeft--;

                    if (tryTimesLeft > 0) {
                        log.error("request rubik grid failed, try times left: {}", tryTimesLeft, e);
                        try {
                            Thread.sleep(30000);
                        } catch (Exception re) {
                            log.error("sleep failed", re);
                            throw new RuntimeException(re);
                        }
                    } else {
                        throw e;
                    }
                }
            }

            log.info("调用rubik/grid云霄接口失败");
            throw new RuntimeException("调用 rubik/grid 失败");
        };

        YunxiaoGridRes.YunxiaoGridDataRes respData = request.apply(new int[]{limit, offset});
        List<DwsYuxiaoRubikGridDfDO> items = ListUtils.newList();
//        YunxiaoGridRes.YunxiaoGridDataRes respData = resp.getData();

        if (respData.getData() != null) {
            items.addAll(respData.getData().stream().map(item -> transform(item, date, instanceModelMap, allPlanZoneInfos)).collect(Collectors.toList()));
        } else {
            log.error("获取 region : " + region + " 下的数据为空，原因：" + respData.getMessage() + respData.getMessage());
            taskLogService.genRunLog("syncYunxiaoRubikGridData", "syncYunxiaoRubikGridData", "获取 region : " + region + " 下的数据为空，原因：" + respData.getMessage() + respData.getMessage());
        }

//        while (respData.getTotalCount() > offset + limit) {
//            offset = offset + limit;
//            YunxiaoGridRes.YunxiaoGridDataRes respLocal = request.apply(new int[]{limit, offset + 1});
//            items.addAll(respLocal.getData().stream().map(item -> transform(item, date, instanceModelMap, allPlanZoneInfos)).collect(Collectors.toList()));
//        }

        return items;
    }

    /**
     * 尝试同步云霄的预扣块信息，如果失败则抛出异常，以便进行重试
     *
     * @param date
     */
    private void trySyncYunxiaoRubikGridData(String date) {
        List<String> regions = getRegionList();
//        List<String> regions = getRegionList().subList(0, 10);
        DictServiceImpl dictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, StaticGinstypeDO> instanceModelMap = dictService.getAllInstanceTypes();
        Map<Long, StaticZoneDO> allPlanZoneInfos = dictService.getAllPlanZoneInfos();

        List<Future<List<DwsYuxiaoRubikGridDfDO>>> allItemDoFutures = ListUtils.newList();
        for (String region : regions) {
            allItemDoFutures.add(threadPool.submit(() -> {
                try {
                    log.info("处理地域开始：{}", region);
                    List<DwsYuxiaoRubikGridDfDO> itemDos = getGridItems(region, date, instanceModelMap, allPlanZoneInfos);
                    // 清洗数据
//                List<DwsYuxiaoRubikGridDfDO> itemDos = items.stream().map(item -> transform(item, date, instanceModelMap, allPlanZoneInfos)).collect(Collectors.toList());
                    log.info("处理地域结束: {}, 数量：{}", region, itemDos.size());
                    return itemDos;
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("清洗数据异常：" + e);
                    throw e;
                }
            }));
        }

        List<String> handledRegions = ListUtils.newList();
        List<DwsYuxiaoRubikGridDfDO> allItemDos = ListUtils.newList();
        for (Future<List<DwsYuxiaoRubikGridDfDO>> future : allItemDoFutures) {
            try {
                allItemDos.addAll(future.get());
                handledRegions.add(allItemDos.get(allItemDos.size() - 1).getRegionName());
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
                log.error("同步云霄预扣块数据失败", e.getMessage());
                throw new RuntimeException("同步云霄预扣块数据失败 " + e.getMessage());
            }
        }

        log.info("预扣块数据入库, size: {}", allItemDos.size());
        saveData(allItemDos);
    }

    /**
     * 清洗云霄返回的数据，转换成需要的格式，并补充查询缺失的字段
     *
     * @param item
     * @return
     */
    private DwsYuxiaoRubikGridDfDO transform(GridItemDTO item, String date, Map<String, StaticGinstypeDO> instanceModelMap, Map<Long, StaticZoneDO> allPlanZoneInfos) {
        DwsYuxiaoRubikGridDfDO result = new DwsYuxiaoRubikGridDfDO();
        log.info("清洗云霄返回的预扣块数据，日期：" + date + "grid id: " + item.getGridId());

        // 1. 根据云霄传回来的实例规格，关联到实例类型
        StaticGinstypeDO instanceInfo = instanceModelMap.get(item.getInstanceType());

        if (instanceInfo != null) {
            log.info("instance info: " + instanceInfo);
            result.setInstanceType(instanceInfo.getGinsfamily());
            // 云霄返回的 instanceType，对应 CRP 中的 instanceModel
            result.setInstanceModel(item.getInstanceType());
        } else {
            result.setInstanceModel(item.getInstanceType());
        }

        // 2. 根据 zoneId 关联到实例类型可用区等等
        StaticZoneDO zoneInfo = allPlanZoneInfos.get(item.getZoneId());

        if (zoneInfo != null) {
            log.info("zoneInfo: " + zoneInfo);
            result.setCustomhouseTitle(zoneInfo.getCustomhouseTitle());
            result.setAreaName(zoneInfo.getAreaName());
            result.setRegionName(zoneInfo.getRegionName());
            result.setZoneName(zoneInfo.getZoneName());
        }

        // 3. 填充其他数据
        result.setCustomerName(item.getGridOwnerName());
        result.setIndustryDept(item.getGridOwnerOrganizationName());
        result.setCustomerShortName(item.getGridOwnerShortName());
        result.setAppid(item.getGridOwner());
        result.setCores(item.getCpu() / 100);
        result.setBurstMem(item.getBurstMem());
        result.setCbsFlag(item.getCbsFlag());
        result.setCpuMode(item.getCpuMode());
        result.setGridId(item.getGridId());
        result.setDeviceId(item.getDeviceId());
        result.setFileFlag(item.getFileFlag());
        result.setLmFlag(item.getLmFlag());
        result.setDeviceClass(item.getDeviceClass());

        Function<Long, Date> fromTimestamp = (Long timestamp) -> {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(timestamp);
            return calendar.getTime();
        };
        result.setGridCreateTime(fromTimestamp.apply(item.getCreateTime()));
        result.setGridUpdateTime(fromTimestamp.apply(item.getUpdateTime()));

        result.setHypervisor(item.getHypervisor());
        result.setHostIp(item.getHostIp());
        result.setMem(item.getMem());
        result.setPool(item.getPool());
        result.setStatus(item.getStatus());
        result.setStatusName(YuxniaoGridStatusEnum.getNameFromCode(item.getStatus()));
        result.setReserveMode(item.getReserveMode());
        result.setReserveModeName(YunxiaoGridReserveModeEnum.getNameFromCode(item.getReserveMode()));
        result.setIdcId(item.getIdcId());
        result.setSubnet(item.getSubnet());
        result.setHypervisor(item.getHypervisor());
        result.setPicoTag(item.getPicoTag());
        result.setPinType(item.getPinType());
        result.setIsReservePackage(item.getIsReservePackage());
        result.setInnerSwitch(item.getInnerSwitch());
        result.setSvrVersion(item.getSvrVersion());
        result.setStatTime(date);
        result.setMatchMode(item.getMatchMode());
        result.setMatchRule(item.getMatchRule());
        result.setNetVersion(item.getNetVersion());
        result.setPicoTagSize(item.getPicoTagSize());
        result.setSupportCvm(item.isSupportCvm());
        result.setSupportEks(item.isSupportEks());
        result.setRackId(item.getRackId());
        result.setVendorModel(item.getVendorModel());
        result.setProductCategory(item.getProductCategory());
        return result;
    }

    /**
     * 写入当天的数据，尝试 3 次，如果都失败，则抛出异常
     *
     * @param data
     */
    private void saveData(List<DwsYuxiaoRubikGridDfDO> data) {
        int i = 0;
        while (i < RETRY_TIMES) {
            try {
                int result = this.ckcldDBHelper.insertBatchWithoutReturnId(data);
                log.info("数据入口成功，入库数量：{}", result);
                return;
            } catch (Exception e) {
                i++;
                log.error("save data error, retry times: {}", i, e);
            }
        }
        throw new RuntimeException("save data error");
    }

    /**
     * 清理当天的数据，尝试 3 次，如果都失败，则抛出异常
     */
    private void clearData(String date) {
        int i = 0;
        while (i < RETRY_TIMES) {
            try {
                ckcldDBHelper.executeRaw(
                        "ALTER TABLE cloud_demand.dws_yunxiao_rubik_grid_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                        date.replace("-", ""));
                return;
            } catch (Exception e) {
                i++;
                log.error("clear data error, retry times: {}", i, e);
            }
        }
        throw new RuntimeException("clear data error");
    }

    // 从云霄获取指定日期的，因为云霄接口返回的是实时的数据，所以如果失败了，需要加重试逻辑
    // 同步流程如下：
    // 1. 获取地域列表，因为云霄接口只支持按照地域来查询预扣信息。
    // 2. 遍历地域列表，依次获取每个地域预扣块的信息。如果查询失败，则重试
    // 3. 清洗预扣数据：
    //    3.1 由实例规格关联出来实例类型
    //    3.2 由 zoneId 获取境内外，地域，可用区等
    // 4. 数据存入 DB。如果整个流程失败，则重试，重试 3 次依然失败，告警。
    @Override
    @TaskLog(taskName = "syncYunxiaoRubikGridData")
    public void syncYunxiaoRubikGridData(String date) {
        log.info("start syncYunxiaoRubikGridData, date: {}", date);
        int tryTimesLeft = RETRY_TIMES;
        long now = System.currentTimeMillis();
        // 清理当天的数据
        clearData(date);

        while (tryTimesLeft > 0) {
            try {
                trySyncYunxiaoRubikGridData(date);
                // 正常结束，直接跳出
                break;
            } catch (Exception e) {
                log.info("syncYunxiaoRubikGridData failed, retry times: {}", tryTimesLeft, e);
                // 重试，先清楚所有当天的数据，然后又再重新生成
                clearData(date);
                tryTimesLeft--;

                if (tryTimesLeft == 0) {
                    log.info("syncYunxiaoRubikGridData failed, date: {}", date, e);
                    taskLogService.genRunLog("syncYunxiaoRubikGridData", "syncYunxiaoRubikGridData", "同步云霄预扣块失败, date: " + date);
                    throw e;
                }
            }
        }

        long seconds = (System.currentTimeMillis() - now) / 1000;

        log.info("syncYunxiaoRubikGridData success in {} s, date: {}", seconds, date);
        taskLogService.genRunLog("syncYunxiaoRubikGridData", "syncYunxiaoRubikGridData", "success in " + seconds + "s, date: " + date);
    }
}
