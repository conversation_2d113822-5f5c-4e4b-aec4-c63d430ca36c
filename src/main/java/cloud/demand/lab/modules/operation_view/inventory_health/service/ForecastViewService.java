package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.CommonQueryConditionDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ForecastWeeklyDemandDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.PurchaseFutureDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.SafetyInventoryDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.TurnoverInventoryDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.future.InventoryHealthActualInvResp;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import java.util.Date;
import java.util.List;

/**
 * 中长尾预测视图Service
 */
public interface ForecastViewService {

    /**
     * 生成中长尾预测周数据，用于计算安全库存
     */
    void genForecastHolidayWeekData();

    /**
     * 生成未来库存-未到货采购数据明细
     */
    void genPurchaseFutureData();

    /**
     * 生成CVM库存预测明细数据
     */
    void genFutureForecastData();

    /**
     * 查询指定日期的实际库存（只要好料的，全量给）
     * 方法返回的结果已经过groupby zone_name、instance_type后处理
     * @param date 必填，查询对应当天的实际库存
     * @param condition 可选，查询条件
     */
    List<DwsActualInventoryDfDO> queryActualInventory(Date date, CommonQueryConditionDTO condition,
            List<String> lineType, List<String> materialType,
            List<String> zoneCategory, List<String> instanceTypeCategory);

    /**
     * 查询指定日期的安全库存，安全库存由以下两部分计算得出
     * 1) 包年包月
     * 2) 弹性用量
     * <br>
     * 说明：该接口适合于历史的安全库存计算
     *
     * @param date 必填，查询对应那周的安全库存
     */
    List<SafetyInventoryDTO> querySafetyInventory(Date date, CommonQueryConditionDTO condition,
            List<String> zoneCategory, List<String> instanceTypeCategory);

    /**
     * 查询未来的安全库存，未来安全库存由以下计算出来（不含弹性用量）：
     * 1) 包年包月
     *
     * @param dates 必填，支持查询多周的安全库存，会查询传入的那一周的安全库存，输出时以相同的date日期标识
     * @param condition 查询条件
     */
    List<SafetyInventoryDTO> queryFutureSafetyInventory(List<Date> dates, CommonQueryConditionDTO condition);

    /**
     * 查询库存健康度 实际库存【前端接口】
     */
    InventoryHealthActualResp queryInventoryHealthActual(InventoryHealthActualReq req);

    /**
     * 查询未来采购单数据
     */
    List<PurchaseFutureDTO> queryPurchaseDataData(List<Date> dates, CommonQueryConditionDTO dto);

    /**
     * 查询13周预测节假周数据
     */
    List<ForecastWeeklyDemandDTO> queryForecastHolidayWeekData(List<Date> dates, CommonQueryConditionDTO dto);

    /**
     * 查询周转库存数据
     */
    List<TurnoverInventoryDTO> queryTurnoverInventory(List<Date> dates, CommonQueryConditionDTO conditionDTO);

    /**
     * 查询剔除了弹性规模的实际库存
     * 2023-06-15:修改为不剔除弹性规模的实际库存
     */
//    InventoryHealthActualInvResp queryActualInventory(CommonQueryConditionDTO condition, List<String> lineType,
//            List<String> materialType, String date);

    /**
     * 查询实际库存，不剔除弹性规模
     */
    InventoryHealthActualInvResp queryActualInventory2(CommonQueryConditionDTO conditionDTO,
            List<String> lineType, List<String> matrialType, List<String> zoneCategory, List<String> instanceTypeCategory);

}
